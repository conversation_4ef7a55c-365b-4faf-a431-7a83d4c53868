@charset "utf-8";
 
*
{

	margin: 0;
	padding: 0;
	/* user-select: none;
	-ms-user-select: none;
	-moz-user-select: none;
	-webkit-user-select: none;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-appearance: none;
	-webkit-backface-visibility: hidden; */
	-webkit-tap-highlight-color: rgba(0,0,0,0);
    font-family: '微软雅黑', sans-serif;

}
.el-button{


}

/* 重置 Element UI Message 样式 */
.el-message {
  top: 20px !important;
  background-color: #fff !important;
  border: 1px solid #ebeef5 !important;
  padding: 15px !important;
  min-width: 380px !important;
  /* box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important; */
}

.el-message__content {
  /* color: #909399 !important; */
  font-size: 14px !important;
  line-height: 1 !important;
  padding: 0 !important;
  margin: 0 !important;
}

.el-message--success {
  background-color: #f0f9eb !important;
  border-color: #e1f3d8 !important;
}

.el-message--warning {
  background-color: #fdf6ec !important;
  border-color: #faecd8 !important;
}

.el-message--error {
  background-color: #fef0f0 !important;
  border-color: #fde2e2 !important;
}

.el-message__icon {
  margin-right: 10px !important;
  font-size: 16px !important;
}


