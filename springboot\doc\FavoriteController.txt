收藏管理接口文档

1. 创建收藏
URL: POST /favorite
请求示例:
{
    "userId": 1,
    "productId": 1
}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "userId": 1,
        "productId": 1
    }
}

2. 更新收藏状态
URL: PUT /favorite/{id}/status?status=1
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "userId": 1,
        "productId": 1,
        "status": 1
    }
}

3. 删除收藏
URL: DELETE /favorite/{id}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": null
}

4. 根据ID获取收藏详情
URL: GET /favorite/{id}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "userId": 1,
        "productId": 1
    }
}

5. 根据用户ID获取收藏列表
URL: GET /favorite/user/{userId}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": [
        {
            "id": 1,
            "userId": 1,
            "productId": 1
        }
    ]
}

6. 分页查询收藏列表
URL: GET /favorite/page?userId=1&currentPage=1&size=10
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "records": [
            {
                "id": 1,
                "userId": 1,
                "productId": 1
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1,
        "pages": 1
    }
}

7. 批量删除收藏
URL: DELETE /favorite/batch?ids=1,2,3
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": null
} 