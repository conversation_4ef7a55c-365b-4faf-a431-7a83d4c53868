评价管理接口文档

1. 创建评价
URL: POST /review
请求示例:
{
    "userId": 1,
    "极客时间
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "userId": 1,
        "productId": 1,
        "content": "非常好吃",
        "rating": 5,
        "status": 1
    }
}

2. 更新评价状态
URL: PUT /review/{id}/status?status=0
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "userId": 1,
        "productId": 1,
        "content": "非常好吃",
        "rating": 5,
        "status": 0
    }
}

3. 删除评价
URL: DELETE /review/{id}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": null
}

4. 根据ID获取评价详情
URL:极客时间
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "userId": 1,
        "productId": 1,
        "content": "非常好吃",
        "rating": 5,
        "status": 1
    }
}

5. 根据商品ID获取评价列表
URL: GET /review/product/{productId}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": [
        {
            "id": 1,
            "userId": 1,
            "productId": 1,
            "content": "非常好吃",
            "rating": 5,
            "status": 1
        }
    ]
}

6. 分页查询评价列表
URL: GET /review/page?productId=1&currentPage=1&size=10
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "records": [
            {
                "id": 1,
                "userId": 1,
                "productId": 1,
                "content": "非常好吃",
                "rating": 5,
                "status": 1
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1,
        "pages": 1
    }
} 