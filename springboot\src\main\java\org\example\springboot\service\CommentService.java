package org.example.springboot.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import org.example.springboot.common.Result;
import org.example.springboot.entity.Article;
import org.example.springboot.entity.Comment;
import org.example.springboot.entity.User;
import org.example.springboot.mapper.ArticleMapper;
import org.example.springboot.mapper.CommentMapper;
import org.example.springboot.mapper.UserMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class CommentService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CommentService.class);

    @Resource
    private CommentMapper commentMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private ArticleMapper articleMapper;

    /**
     * 创建评论
     */
    public Result<?> createComment(Comment comment) {
        try {
            // 检查用户是否存在
            User user = userMapper.selectById(comment.getUserId());
            if (user == null) {
                return Result.error("-1", "用户不存在");
            }

            // 检查文章是否存在
            Article article = articleMapper.selectById(comment.getArticleId());
            if (article == null) {
                return Result.error("-1", "文章不存在");
            }

            // 如果是回复其他评论，检查父评论是否存在
            if (comment.getParentId() != null) {
                Comment parentComment = commentMapper.selectById(comment.getParentId());
                if (parentComment == null) {
                    return Result.error("-1", "回复的评论不存在");
                }
            }

            // 设置创建时间
            comment.setCreatedAt(new Timestamp(System.currentTimeMillis()));
            comment.setUpdatedAt(new Timestamp(System.currentTimeMillis()));
            comment.setLikeCount(0);
            comment.setStatus(1);

            int result = commentMapper.insert(comment);
            if (result > 0) {
                LOGGER.info("创建评论成功，评论ID：{}", comment.getId());
                return Result.success(comment);
            }
            return Result.error("-1", "创建评论失败");
        } catch (Exception e) {
            LOGGER.error("创建评论失败：{}", e.getMessage());
            return Result.error("-1", "创建评论失败：" + e.getMessage());
        }
    }

    /**
     * 删除评论
     */
    public Result<?> deleteComment(Long id) {
        try {
            Comment comment = commentMapper.selectById(id);
            if (comment == null) {
                return Result.error("-1", "评论不存在");
            }

            // 删除该评论下的所有回复
            LambdaQueryWrapper<Comment> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Comment::getParentId, id);
            commentMapper.delete(queryWrapper);

            // 删除评论本身
            int result = commentMapper.deleteById(id);
            if (result > 0) {
                LOGGER.info("删除评论成功，评论ID：{}", id);
                return Result.success();
            }
            return Result.error("-1", "删除评论失败");
        } catch (Exception e) {
            LOGGER.error("删除评论失败：{}", e.getMessage());
            return Result.error("-1", "删除评论失败：" + e.getMessage());
        }
    }

    /**
     * 更新评论
     */
    public Result<?> updateComment(Long id, Comment comment) {
        try {
            Comment existingComment = commentMapper.selectById(id);
            if (existingComment == null) {
                return Result.error("-1", "评论不存在");
            }

            // 只允许更新内容和状态
            existingComment.setContent(comment.getContent());
            if (comment.getStatus() != null) {
                existingComment.setStatus(comment.getStatus());
            }
            existingComment.setUpdatedAt(new Timestamp(System.currentTimeMillis()));

            int result = commentMapper.updateById(existingComment);
            if (result > 0) {
                LOGGER.info("更新评论成功，评论ID：{}", id);
                return Result.success(existingComment);
            }
            return Result.error("-1", "更新评论失败");
        } catch (Exception e) {
            LOGGER.error("更新评论失败：{}", e.getMessage());
            return Result.error("-1", "更新评论失败：" + e.getMessage());
        }
    }

    /**
     * 获取评论列表（带分页）
     */
    public Page<Comment> getCommentsByPage(Long articleId, Integer status, Integer currentPage, Integer size) {
        try {
            Page<Comment> page = new Page<>(currentPage, size);
            LambdaQueryWrapper<Comment> queryWrapper = new LambdaQueryWrapper<>();
            
            // 只查询顶级评论（父评论为空）
            queryWrapper.isNull(Comment::getParentId);
            
            if (articleId != null) {
                queryWrapper.eq(Comment::getArticleId, articleId);
            }
            if (status != null) {
                queryWrapper.eq(Comment::getStatus, status);
            }
            
            // 按创建时间倒序排列
            queryWrapper.orderByDesc(Comment::getCreatedAt);
            
            Page<Comment> result = commentMapper.selectPage(page, queryWrapper);
            
            // 填充用户信息和回复信息
            fillUserInfoAndReplies(result.getRecords());
            
            return result;
        } catch (Exception e) {
            LOGGER.error("获取评论列表失败：{}", e.getMessage());
            return new Page<>();
        }
    }

    /**
     * 获取文章所有评论（包括回复，树形结构）
     */
    public Result<?> getCommentsByArticleId(Long articleId) {
        try {
            // 检查文章是否存在
            Article article = articleMapper.selectById(articleId);
            if (article == null) {
                return Result.error("-1", "文章不存在");
            }

            // 查询文章的所有评论
            LambdaQueryWrapper<Comment> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Comment::getArticleId, articleId);
            queryWrapper.eq(Comment::getStatus, 1); // 只查询状态为显示的评论
            queryWrapper.orderByDesc(Comment::getCreatedAt);
            List<Comment> allComments = commentMapper.selectList(queryWrapper);

            // 构建评论树
            List<Comment> rootComments = buildCommentTree(allComments);
            
            return Result.success(rootComments);
        } catch (Exception e) {
            LOGGER.error("获取文章评论失败：{}", e.getMessage());
            return Result.error("-1", "获取文章评论失败：" + e.getMessage());
        }
    }

    /**
     * 构建评论树形结构
     */
    private List<Comment> buildCommentTree(List<Comment> allComments) {
        List<Comment> rootComments = new ArrayList<>();
        Map<Long, Comment> commentMap = new HashMap<>();
        
        // 填充用户信息
        for (Comment comment : allComments) {
            User user = userMapper.selectById(comment.getUserId());
            if (user != null) {
                // 隐藏敏感信息
                user.setPassword(null);
                user.setEmail(null);
                comment.setUser(user);
            }
            comment.setReplies(new ArrayList<>());
            commentMap.put(comment.getId(), comment);
        }
        
        // 构建树形结构
        for (Comment comment : allComments) {
            if (comment.getParentId() == null) {
                // 顶级评论
                rootComments.add(comment);
            } else {
                // 回复评论
                Comment parentComment = commentMap.get(comment.getParentId());
                if (parentComment != null) {
                    parentComment.getReplies().add(comment);
                }
            }
        }
        
        return rootComments;
    }

    /**
     * 填充用户信息和回复
     */
    private void fillUserInfoAndReplies(List<Comment> comments) {
        for (Comment comment : comments) {
            // 填充用户信息
            User user = userMapper.selectById(comment.getUserId());
            if (user != null) {
                // 隐藏敏感信息
                user.setPassword(null);
                comment.setUser(user);
            }
            
            // 查询回复
            LambdaQueryWrapper<Comment> replyWrapper = new LambdaQueryWrapper<>();
            replyWrapper.eq(Comment::getParentId, comment.getId());
            replyWrapper.eq(Comment::getStatus, 1);
            replyWrapper.orderByAsc(Comment::getCreatedAt);
            List<Comment> replies = commentMapper.selectList(replyWrapper);
            
            // 填充回复的用户信息
            for (Comment reply : replies) {
                User replyUser = userMapper.selectById(reply.getUserId());
                if (replyUser != null) {
                    replyUser.setPassword(null);
                    reply.setUser(replyUser);
                }
            }
            
            comment.setReplies(replies);
        }
    }

    /**
     * 点赞评论
     */
    public Result<?> likeComment(Long id) {
        try {
            Comment comment = commentMapper.selectById(id);
            if (comment == null) {
                return Result.error("-1", "评论不存在");
            }
            
            comment.setLikeCount(comment.getLikeCount() + 1);
            commentMapper.updateById(comment);
            
            return Result.success(comment);
        } catch (Exception e) {
            LOGGER.error("点赞评论失败：{}", e.getMessage());
            return Result.error("-1", "点赞评论失败：" + e.getMessage());
        }
    }
} 