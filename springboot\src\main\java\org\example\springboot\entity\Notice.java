package org.example.springboot.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Schema(description="通知类实体")
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("notice")
public class Notice {
    @TableId(type = IdType.AUTO)
    @Schema(description = "自增主键")
    private int id;
    @Schema(description = "通知标题")
    private String title;
    @Schema(description = "通知内容")
    private String content;
    @JsonFormat(locale="zh",timezone="GMT+8", pattern="yyyy-MM-dd HH:mm")
    @Schema(description = "发布时间")
    private Date time;
    @Schema(description = "通知标签")
    private String tags;
}
