订单管理接口文档

1. 创建订单
URL: POST /order
请求示例:
{
    "userId": 1,
    "productId": 1,
    "quantity": 2,
    "totalPrice": 100.00
}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "userId": 1,
        "productId": 1,
        "quantity": 2,
        "totalPrice": 100.00,
        "status": 0
    }
}

2. 更新订单状态
URL: PUT /order/{id}/status?status=1
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "userId": 1,
        "productId": 1,
        "quantity": 2,
        "totalPrice": 100.00,
        "status": 1
    }
}

3. 删除订单
URL: DELETE /order/{id}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": null
}

4. 根据ID获取订单详情
URL: GET /order/{id}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "userId": 1,
        "productId": 1,
        "quantity": 2,
        "totalPrice": 100.00,
        "status": 0
    }
}

5. 根据用户ID获取订单列表
URL: GET /order/user/{userId}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": [
        {
            "id": 1,
            "userId": 1,
            "productId": 1,
            "quantity": 2,
            "totalPrice": 100.00,
            "status": 0
        }
    ]
}

6. 分页查询订单列表
URL: GET /order/page?userId=1&currentPage=1&size=10
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "records": [
            {
                "id": 1,
                "userId": 1,
                "productId": 1,
                "quantity": 2,
                "totalPrice": 100.00,
                "status": 0
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1,
        "pages": 1
    }
}

7. 申请退款
URL: POST /order/{id}/refund?reason=商品质量问题
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "userId": 1,
        "productId": 1,
        "quantity": 2,
        "totalPrice": 100.00,
        "status": 4,
        "refundReason": "商品质量问题"
    }
}

8. 批量删除订单
URL: DELETE /order/batch?ids=1,2,3
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": null
}

9. 获取订单物流信息
URL: GET /order/{id}/logistics
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "orderId": 1,
        "addressId": 1,
        "companyName": "顺丰快递",
        "trackingNumber": "SF1234567890",
        "status": 1,
        "createdAt": "2024-01-01 12:00:00",
        "updatedAt": "2024-01-01 12:00:00",
        "order": {
            // 订单详细信息
        },
        "address": {
            // 地址详细信息
        }
    }
} 