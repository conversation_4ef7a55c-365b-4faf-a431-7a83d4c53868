{"name": "vue_template", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "axios": "^1.7.9", "core-js": "^3.8.3", "dompurify": "^3.2.4", "echarts": "^5.6.0", "element-ui": "^2.15.14", "file-saver": "^2.0.5", "quill": "^2.0.3", "quill-better-table": "^1.2.10", "quill-table": "^1.0.0", "quill-table-ui": "^1.0.7", "sass": "^1.83.4", "vue": "^2.6.14", "vue-count-to": "^1.0.13", "vue-cropper": "^0.5.8", "vue-quill-editor": "^3.0.6", "vue-router": "^3.5.1", "vuex": "^3.6.2", "xlsx": "^0.15.1"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "less": "^4.0.0", "less-loader": "^8.0.0", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}