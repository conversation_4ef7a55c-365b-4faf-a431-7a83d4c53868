spring.application.name=springboot
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.enable-spring-security=false
spring.security.user.name=admin
spring.security.user.password=admin
springdoc.swagger-ui.tags-sorter=alpha

springdoc.swagger-ui.operations-sorter= alpha
springdoc.swagger-ui.enabled= true
springdoc.api-docs.path=/v3/api-docs
springdoc.api-docs.enabled=true
springdoc.paths-to-match= /**
#springdoc.group-configs= platform
springdoc.packages-to-scan=org.example.springboot

spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=****************************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456
mybatis.configuration.map-underscore-to-camel-case=true
mybatis.configuration.use-built-in-alias-types=true
server.port=1234

user.defaultPassword=123456
spring.mail.host=smtp.qq.com
spring.mail.username=<EMAIL>
spring.mail.password=kutsaiadfvqyjajc
spring.mail.default-encoding=utf-8
spring.mail.properties.mail.smtp.socketFactory.class=javax.net.ssl.SSLSocketFactory
spring.mail.properties.mail.debug=true
spring.mail.properties.mail.smtp.ssl.protocols=TLSv1.2
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.ssl.enable=true
spring.mail.properties.mail.smtp.socketFactory.port=465
spring.mail.port=587
user.fromEmail=<EMAIL>


# application.properties
server.servlet.context-path=/
spring.mvc.static-path-pattern=/**
spring.web.resources.static-locations=classpath:/static/
mybatis-plus.mapper-locations=classpath:/mapper/*.xml
spring.jackson.default-property-inclusion=non_empty

# application.properties
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB


knife4j.enable=true
knife4j.setting.language=zh_cn
knife4j.basic.enable=true
knife4j.basic.username=admin
knife4j.basic.password=admin

alipay.app-id=9021000140623471
alipay.app-private-key=MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCQpuLhEYrpuaAKHF5D4EZ2ObgVqy5PFaBCsH8Y8ep5JF2MDHn/rRG6YXaw23WncnUn/6yFlwdpwRAHojWUlK33XxLEFR9V4q5QfDh2URPp6JQEp8fuWroncUziHnPpV/heFN02NWcwr1j729NghwOuYeJAWg4WxJH5521WRyBPEPN/WErthFFUojLH3mkaJ19n7bR4ARnNLQ0AP7cYfh0x6apNwzPGj7GbVgaxW0mQMXl6PrvZ4twoBhZwaUQFAfhWqQtWV1VwLt+Of0n7SbuKj6ohLb8O50VBlYf+nTC2aSBtv3G1atigGUciAqiU19yH6VGrKJJ7zzDCmSwPxJYxAgMBAAECggEAPJSYGjiVm/Mw0ms1E2o/OE5OCU0q1NYyyxtN722JjHitcPTctzaNYShmZAVVTe6CoGDUqRIqeaDwjLHjjLmD7kD0BAjwmbCY3n5Xyz2DgIf2TcjS08FNiy1JTL4CP2ynSQel6YkOEXauZrSW3ytMYGEAN4E/u0tR83537RVWs6W5p7BU8Fsl+p7zVdEClfahgLjjidVVApVi5u/YXydHVyTDfQpc9XqA4oQt2hhC3uLwiFnbOZRp2spDrtcTvC5A4/kXdc1YJ7Rxpi7Swxwyt1S5UWOQcDQHyfVE5XeVn9Il6ucv7syQuYe4aqrlYO6OVW3w0j77bhvZxJpMoM+zvQKBgQDv3URjw/bxVoVlHZ/58VUyv8R0OgQ0ZLcShktoJHEMe7ggD9gfit3LkoNrY7eH4LLyZNS7C5GKJ/pqtSeRnovqKQeSSft+OxCzIyXsIzJCz53yteghcdt4e0hjd3tcH8AiKwqLB3Wjv2CdUuwLd4djqlWcmBdMdvoH/ALgxwxFpwKBgQCaYfRMCZZrSjQQxB+O0NY1bivN24q5lxxz5KPBycZS/saZaziiM7KCwcmCY1yB59mjzu0BeTFhYOy6gSt+O0blpmg4IgSdRG1k8eDXvXtgHHbC0zH5d/UR5cn6PcxbOI+86ieNCYa9wGrlDQRjYa6urs76fn03+xVP3t1kZDjwZwKBgGiRww3nUdHW0WLKroKNdoIgXin5XPnmanV1/IvTT0GM8jFwFt1xcc4c+pQnrcxb5keMM72zhmh/k0lP3oc6Ym7cIkPhYvWeFGK0UQUINxIvZwF30lI6S97TiDRs02fPI7kBoKAJyXxwo1MS4nsBShHmIVqaZ5XbTJeMg0ixHiWDAoGAJAXPNx7VlltOIstggbaBBR6b4gDkMcHsxa4H9+PY/uyr8ohuQorFkHUfS+lbYIR06CAUdU+OEibHlg0l1OKH/HYDw3VMllWtIOy/hheUfiymGXemJYu0B2US5+0bjf14rKAGcoZShlwrlR6gc4ALfn8TlKeHGFTh+C0Mg6xPDOkCgYEA6zuA5tCxp+/4vH6XZ+HX1FVCQClZqJo4Xd4NXrHaLPSc2D7x5vV31BqTRXDyhugzVLJs6lBsR+n42HCZCKcIKebNSmpAktgA4P0b8pk8YAqU9abv2PMdDwe//jV7aXI1m1pFnkMOV5qoB27bgdq1tQRd1INg8EnVfEU6taE0Xj8=
alipay.alipay-public-key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAiMD4FvmHpaJFAYYb83fUY0kIZSWqARP9rKIYgkbQ7PYF5VsrmTwd8TftQxOVrz+YjR+md7neDmPCDsMNXVGQLXd5mUqVF5UC6+jVuNI3f8abJfG3FZd8BteKdOnCLoZj+D7ZZv6DFFNKAvCeXbXqhCuKpXA/+FdndLEGqcGWST/+dJJ8UxVACK3LOz6/HAIncMv1uoU7fpgqOpzMnVkAB9VGJqaTvG4QK6jy453/j0Z05HpR9MJGx2cfcLNmX5mQHEngmbPxk4ny51RthMkodAoFIKhfQFkZvNx9IJvsy8UveNT58wrNBv3fxsAXgiakNEYH//QtLRf6+D+hwFMI5QIDAQAB
alipay.notifyUrl=http://s728ba36.natappfree.cc/api/alipay/notify

spring.cache.type=caffeine
spring.cache.caffeine.spec=maximumSize=500,expireAfterWrite=10m
#logging.level.org.example.springboot=DEBUG
