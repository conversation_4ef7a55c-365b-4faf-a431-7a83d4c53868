商品管理接口文档

1. 创建商品
URL: POST /product
请求示例:
{
    "name": "苹果",
    "description": "新鲜红富士苹果",
    "price": 10.00,
    "stock": 100,
    "categoryId": 1,
    "farmerId": 1
}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "name": "苹果",
        "description": "新鲜红富士苹果",
        "price": 10.00,
        "stock": 100,
        "categoryId": 1,
        "farmerId": 1,
        "status": 1
    }
}

2. 更新商品信息
URL: PUT /product/{id}
请求示例:
{
    "name": "红富士苹果",
    "description": "新鲜红富士苹果，产地山东"
}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "name": "红富士苹果",
        "description": "新鲜红富士苹果，产地山东",
        "price": 10.00,
        "stock": 100,
        "categoryId": 1,
        "farmerId": 1,
        "status": 1
    }
}

3. 删除商品
URL: DELETE /product/{id}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": null
}

4. 根据ID获取商品详情
URL: GET /product/{id}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "name": "红富士苹果",
        "description": "新鲜红富士苹果，产地山东",
        "price": 10.00,
        "stock": 100,
        "categoryId": 1,
        "farmerId": 1,
        "status": 1
    }
}

5. 分页查询商品列表
URL: GET /product/page?name=苹果&categoryId=1&farmerId=1&status=1&currentPage=1&size=10
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "records": [
            {
                "id": 1,
                "name": "红富士苹果",
                "description": "新鲜红富士苹果，产地山东",
                "price": 10.00,
                "stock": 100,
                "categoryId": 1,
                "farmerId": 1,
                "status": 1
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1,
        "pages": 1
    }
}

6. 更新商品状态
URL: PUT /product/{id}/status?status=0
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "name": "红富士苹果",
        "description": "新鲜红富士苹果，产地山东",
        "price": 10.00,
        "stock": 100,
        "categoryId": 1,
        "farmerId": 1,
        "status": 0
    }
}

7. 批量删除商品
URL: DELETE /product/batch?ids=1,2,3
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": null
}

8. 批量更新商品状态
URL: PUT /product/batch/status?ids=1,2,3&status=0
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": null
} 