<template>
  <div class="dashboard-wrapper">
    <!-- 页面标题 -->
    <div class="dashboard-header">
      <h1 class="dashboard-title">沙棘健康后台管理系统</h1>
      <div class="dashboard-subtitle">欢迎回来，{{ userInfo ? userInfo.name : '管理员' }}</div>
    </div>
    
    <!-- 数据概览卡片 -->
    <div class="stat-cards">
      <el-card class="stat-card" shadow="hover">
        <div class="stat-header">
          <i class="el-icon-shopping-cart-full stat-icon"></i>
          <div class="stat-title">本月订单</div>
        </div>
        <div class="stat-value">
          <count-to :startVal="0" :endVal="orderStats.currentMonthOrders" :duration="2000">
          </count-to>
        </div>
        <div class="stat-footer">
          较上月
          <span :class="orderTrend >= 0 ? 'up' : 'down'">
            {{ orderStats.growthRate }}
            <i :class="orderTrend >= 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
          </span>
        </div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <div class="stat-header">
          <i class="el-icon-money stat-icon"></i>
          <div class="stat-title">本月销售额</div>
        </div>
        <div class="stat-value">
          ¥<count-to :startVal="0" :endVal="salesStats.currentMonthSales" :duration="2000" :decimals="2">
          </count-to>
        </div>
        <div class="stat-footer">
          较上月
          <span :class="saleTrend >= 0 ? 'up' : 'down'">
            {{ salesStats.growthRate }}
            <i :class="saleTrend >= 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
          </span>
        </div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <div class="stat-header">
          <i class="el-icon-user stat-icon"></i>
          <div class="stat-title">今年用户数</div>
        </div>
        <div class="stat-value">
          <count-to :startVal="0" :endVal="userStats.currentYearUsers" :duration="2000">
          </count-to>
        </div>
        <div class="stat-footer">
          较去年
          <span :class="userTrend >= 0 ? 'up' : 'down'">
            {{ userStats.growthRate }}
            <i :class="userTrend >= 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
          </span>
        </div>
      </el-card>
    </div>

    <div class="content-wrapper">
      <!-- 热销商品TOP5图表 -->
      <el-card class="chart-card" shadow="hover">
        <div slot="header" class="chart-header">
          <span>热销商品 TOP5</span>
          <el-button type="text" @click="fetchTopProducts">刷新</el-button>
        </div>
        <div class="chart-content">
          <div ref="topProductsChart" class="chart"></div>
        </div>
      </el-card>

      <!-- 品类销售占比图表 -->
      <el-card class="chart-card" shadow="hover">
        <div slot="header" class="chart-header">
          <span>品类销售占比</span>
          <el-button type="text" @click="fetchCategoryStats">刷新</el-button>
        </div>
        <div class="chart-content">
          <div ref="categoryChart" class="chart"></div>
        </div>
      </el-card>
    </div>

    <!-- 通知公告 -->
    <div class="content-wrapper">
      <el-card class="notice-card" shadow="hover">
        <div slot="header" class="notice-header">
          <span>通知公告</span>
          <el-button type="text" @click="fetchData">刷新</el-button>
        </div>
        <div class="notice-content">
          <el-timeline>
            <el-timeline-item v-for="(notice, index) in announcements" :key="index" :timestamp="notice.time" :type="getNoticeType(notice.type)">
              <el-card class="notice-item" shadow="never">
                <h4>{{ notice.title }}</h4>
                <p class="notice-text">{{ notice.content }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { Timeline, TimelineItem, Card, Button } from 'element-ui'
import CountTo from 'vue-count-to'
import Request from '../utils/request.js'
import * as echarts from 'echarts'

export default {
  name: 'ShowView',
  components: {
    [Card.name]: Card,
    [Timeline.name]: Timeline,
    [TimelineItem.name]: TimelineItem,
    [Button.name]: Button,
    CountTo
  },
  data() {
    return {
      userInfo: JSON.parse(localStorage.getItem("backUser") || '{}'),
      noticeLimit: 10,
      announcements: [],
      // 统计数据
      orderStats: {
        currentMonthOrders: 0,
        lastMonthOrders: 0,
        growthRate: '0.00%'
      },
      salesStats: {
        currentMonthSales: 0,
        lastMonthSales: 0,
        growthRate: '0.00%'
      },
      userStats: {
        currentYearUsers: 0,
        lastYearUsers: 0,
        growthRate: '0.00%'
      },
      // 热销商品数据
      topProductsChart: null,
      topProducts: [],
      // 添加品类统计数据
      categoryChart: null,
      categoryStats: []
    }
  },
  computed: {
    // 计算订单增长率数值（去掉百分号）
    orderTrend() {
      return parseFloat(this.orderStats.growthRate)
    },
    // 计算销售额增长率数值
    saleTrend() {
      return parseFloat(this.salesStats.growthRate)
    },
    // 计算用户增长率数值
    userTrend() {
      return parseFloat(this.userStats.growthRate)
    }
  },
  created() {
    this.fetchData()
    this.fetchStatistics()
    this.fetchTopProducts()
    this.fetchCategoryStats()
  },
  mounted() {
    this.initTopProductsChart()
    this.initCategoryChart()
    window.addEventListener('resize', this.resizeCharts)
  },
  beforeDestroy() {
    if (this.topProductsChart) {
      this.topProductsChart.dispose()
    }
    if (this.categoryChart) {
      this.categoryChart.dispose()
    }
    window.removeEventListener('resize', this.resizeCharts)
  },
  methods: {
    // 获取通知数据
    fetchData() {
      Request.get("/notice/limit", {
        params: {
          count: this.noticeLimit
        }
      }).then(response => {
        if (response.code === '0') {
          this.announcements = response.data
        }
      })
    },
    // 获取统计数据
    fetchStatistics() {
      // 获取订单统计
      Request.get("/statistics/orders/monthly").then(response => {
        if (response.code === '0') {
          this.orderStats = response.data
        }
      })

      // 获取销售额统计
      Request.get("/statistics/sales/monthly").then(response => {
        if (response.code === '0') {
          this.salesStats = response.data
        }
      })

      // 获取用户统计
      Request.get("/statistics/users/yearly").then(response => {
        if (response.code === '0') {
          this.userStats = response.data
        }
      })
    },
    getNoticeType(type) {
      const types = {
        1: 'primary',   // 普通通知
        2: 'success',   // 活动通知
        3: 'warning',   // 重要通知
        4: 'danger'     // 紧急通知
      }
      return types[type] || 'primary'
    },
    // 初始化图表
    initTopProductsChart() {
      this.topProductsChart = echarts.init(this.$refs.topProductsChart)
      this.updateTopProductsChart()
    },

    // 更新图表数据
    updateTopProductsChart() {
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: '{b}: {c}件'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            formatter: (value) => value + '件'
          }
        },
        yAxis: {
          type: 'category',
          data: this.topProducts.map(item => item.name).reverse(),
          axisLabel: {
            formatter: (value) => {
              if (value.length > 10) {
                return value.substring(0, 10) + '...'
              }
              return value
            }
          }
        },
        series: [{
          name: '销售数量',
          type: 'bar',
          data: this.topProducts.map(item => item.salesCount).reverse(),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#83bff6' },
              { offset: 0.5, color: '#188df0' },
              { offset: 1, color: '#188df0' }
            ])
          },
          emphasis: {
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: '#2378f7' },
                { offset: 0.7, color: '#2378f7' },
                { offset: 1, color: '#83bff6' }
              ])
            }
          }
        }]
      }
      this.topProductsChart.setOption(option)
    },

    // 获取热销商品数据
    async fetchTopProducts() {
      try {
        const response = await Request.get('/statistics/products/top5')
        if (response.code === '0' && response.data.topProducts) {
          this.topProducts = response.data.topProducts
          this.$nextTick(() => {
            this.updateTopProductsChart()
          })
        }
      } catch (error) {
        console.error('获取热销商品数据失败:', error)
      }
    },

    // 初始化品类图表
    initCategoryChart() {
      this.categoryChart = echarts.init(this.$refs.categoryChart)
      this.updateCategoryChart()
    },

    // 更新品类图表数据
    updateCategoryChart() {
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center'
        },
        series: [
          {
            name: '品类销售',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true,
              formatter: '{b}: {d}%'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '14',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: true
            },
            data: this.categoryStats.map(item => ({
              name: item.name,
              value: item.salesCount
            }))
          }
        ],
        color: [
          '#409EFF', '#67C23A', '#E6A23C', '#F56C6C',
          '#909399', '#36CBCB', '#FFA2D3', '#9A60B4'
        ]
      }
      this.categoryChart.setOption(option)
    },

    // 获取品类统计数据
    async fetchCategoryStats() {
      try {
        const response = await Request.get('/statistics/category/sales')
        if (response.code === '0' && response.data.categoryStats) {
          this.categoryStats = response.data.categoryStats
          this.$nextTick(() => {
            this.updateCategoryChart()
          })
        }
      } catch (error) {
        console.error('获取品类统计数据失败:', error)
      }
    },

    // 统一处理图表缩放
    resizeCharts() {
      if (this.topProductsChart) {
        this.topProductsChart.resize()
      }
      if (this.categoryChart) {
        this.categoryChart.resize()
      }
    }
  }
}
</script>

<style lang="less" scoped>
.dashboard-wrapper {
  padding: 16px;
  min-height: 100%;
}

.dashboard-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.dashboard-title {
  font-size: 24px;
  color: #303133;
  margin: 0 0 8px 0;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.dashboard-title:before {
  content: "";
  display: inline-block;
  width: 4px;
  height: 24px;
  background: linear-gradient(to bottom, #f56c0c, #e6a23c);
  margin-right: 12px;
  border-radius: 2px;
}

.dashboard-subtitle {
  color: #909399;
  font-size: 14px;
}

.stat-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 12px;
  border: none;
  transition: all 0.3s ease;
  padding: 8px;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(245, 108, 12, 0.05), rgba(245, 108, 12, 0.01));
  z-index: 0;
}

.stat-card::after {
  content: '';
  position: absolute;
  right: -20px;
  top: -20px;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(245, 108, 12, 0.15), transparent 70%);
  z-index: 0;
}

  .stat-header {
    display: flex;
    align-items: center;
  gap: 14px;
  position: relative;
  z-index: 1;
    margin-bottom: 16px;
  }

  .stat-icon {
  font-size: 22px;
  width: 42px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f56c0c, #e6a23c);
  color: white;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(245, 108, 12, 0.2);
  }

  .stat-title {
    font-size: 16px;
    color: #606266;
  font-weight: 500;
  }

  .stat-value {
  font-size: 32px;
  font-weight: 600;
  margin: 16px 0;
    color: #303133;
  position: relative;
  z-index: 1;
  }

  .stat-footer {
  color: #909399;
    font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  z-index: 1;
}

.stat-footer .up {
  color: #f56c0c;
  display: flex;
  align-items: center;
  gap: 4px;
    }

.stat-footer .down {
  color: #409eff;
  display: flex;
  align-items: center;
  gap: 4px;
}

.content-wrapper {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

@media (max-width: 992px) {
  .content-wrapper {
    grid-template-columns: 1fr;
  }
}

.chart-card, .notice-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.chart-card:hover, .notice-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.chart-header, .notice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f2f5;
  padding-bottom: 12px;
}

.chart-header span, .notice-header span {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  position: relative;
  padding-left: 12px;
}

.chart-header span::before, .notice-header span::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(to bottom, #f56c0c, #e6a23c);
  border-radius: 2px;
}

.chart-content {
  height: 350px;
  position: relative;
}

  .chart {
    width: 100%;
    height: 100%;
}

.notice-content {
  padding: 16px 0;
  max-height: 450px;
  overflow-y: auto;
}

.notice-item {
  margin-bottom: 20px;
  position: relative;
}

.notice-item h4 {
    margin: 0 0 8px;
  font-size: 16px;
  font-weight: 600;
    color: #303133;
  }

.notice-item:hover h4 {
  color: #f56c0c;
}

  .notice-text {
    color: #606266;
  font-size: 14px;
  line-height: 1.6;
    margin: 0;
  padding-right: 16px;
  }

:deep(.el-timeline-item__tail) {
  border-left: 2px solid #e6a23c;
}

:deep(.el-timeline-item__node--primary) {
  background-color: #409eff;
}

:deep(.el-timeline-item__node--success) {
  background-color: #67c23a;
}

:deep(.el-timeline-item__node--warning) {
  background-color: #e6a23c;
}

:deep(.el-timeline-item__node--danger) {
  background-color: #f56c6c;
}

:deep(.el-button--text) {
  color: #f56c0c;
}

:deep(.el-button--text:hover) {
  color: #e6a23c;
}

:deep(.el-card__header) {
  padding: 16px 20px;
}

:deep(.el-card__body) {
  padding: 20px;
}

@media (max-width: 768px) {
  .stat-cards {
    grid-template-columns: 1fr;
  }
  
  .stat-value {
    font-size: 24px;
  }
  
  .chart-header span, .notice-header span {
    font-size: 14px;
  }
  
  .notice-content {
    max-height: 300px;
  }
}
</style>