package org.example.springboot.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.example.springboot.common.Result;
import org.example.springboot.entity.Comment;
import org.example.springboot.service.CommentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Tag(name = "评论管理接口")
@RestController
@RequestMapping("/comment")
public class CommentController {
    private static final Logger LOGGER = LoggerFactory.getLogger(CommentController.class);

    @Autowired
    private CommentService commentService;

    @Operation(summary = "创建评论")
    @PostMapping
    public Result<?> createComment(@RequestBody Comment comment) {
        return commentService.createComment(comment);
    }

    @Operation(summary = "删除评论")
    @DeleteMapping("/{id}")
    public Result<?> deleteComment(@PathVariable Long id) {
        return commentService.deleteComment(id);
    }

    @Operation(summary = "修改评论")
    @PutMapping("/{id}")
    public Result<?> updateComment(@PathVariable Long id, @RequestBody Comment comment) {
        return commentService.updateComment(id, comment);
    }

    @Operation(summary = "获取文章的评论列表（带分页，包括回复）")
    @GetMapping("/page")
    public Result<?> getCommentsByPage(
            @RequestParam(required = false) Long articleId,
            @RequestParam(required = false) Integer status,
            @RequestParam(defaultValue = "1") Integer currentPage,
            @RequestParam(defaultValue = "10") Integer size) {
        return Result.success(commentService.getCommentsByPage(articleId, status, currentPage, size));
    }

    @Operation(summary = "获取文章所有评论（树形结构）")
    @GetMapping("/article/{articleId}")
    public Result<?> getCommentsByArticleId(@PathVariable Long articleId) {
        return commentService.getCommentsByArticleId(articleId);
    }

    @Operation(summary = "点赞评论")
    @PostMapping("/{id}/like")
    public Result<?> likeComment(@PathVariable Long id) {
        return commentService.likeComment(id);
    }
} 