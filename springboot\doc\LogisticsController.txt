物流管理接口文档

1. 创建物流信息
URL: POST /logistics
请求示例:
{
    "orderId": 1,
    "receiverName": "张三",
    "receiverPhone": "13800138000",
    "receiverAddress": "北京市朝阳区xxx街道xxx号",
    "companyName": "顺丰快递",
    "trackingNumber": "SF1234567890",
    "expectedArrivalTime": "2024-01-02 14:00:00",
    "status": 0
}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "orderId": 1,
        "receiverName": "张三",
        "receiverPhone": "13800138000",
        "receiverAddress": "北京市朝阳区xxx街道xxx号",
        "companyName": "顺丰快递",
        "trackingNumber": "SF1234567890",
        "expectedArrivalTime": "2024-01-02 14:00:00",
        "status": 0,
        "createdAt": "2024-01-01 12:00:00",
        "updatedAt": "2024-01-01 12:00:00"
    }
}

2. 更新物流状态
URL: PUT /logistics/{id}/status?status=1
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "status": 1,
        "order": {
            "id": 1,
            "status": 2
        }
    }
}

3. 确认签收
URL: PUT /logistics/{id}/sign
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "status": 2,
        "order": {
            "id": 1,
            "status": 3
        }
    }
}

4. 删除物流信息
URL: DELETE /logistics/{id}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": null
}

5. 根据ID获取物流详情
URL: GET /logistics/{id}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "orderId": 1,
        "addressId": 1,
        "companyName": "顺丰快递",
        "trackingNumber": "SF1234567890",
        "expectedArrivalTime": "2024-01-02 14:00:00",
        "status": 1,
        "createdAt": "2024-01-01 12:00:00",
        "updatedAt": "2024-01-01 12:00:00",
        "order": {
            // 订单详细信息
        },
        "address": {
            // 地址详细信息
        }
    }
}

6. 根据订单ID获取物流信息
URL: GET /logistics/order/{orderId}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "orderId": 1,
        "addressId": 1,
        "companyName": "顺丰快递",
        "trackingNumber": "SF1234567890",
        "expectedArrivalTime": "2024-01-02 14:00:00",
        "status": 1,
        "createdAt": "2024-01-01 12:00:00",
        "updatedAt": "2024-01-01 12:00:00",
        "order": {
            // 订单详细信息
        },
        "address": {
            // 地址详细信息
        }
    }
}

7. 分页查询物流信息
URL: GET /logistics/page?orderId=1&farmerId=1&status=1&currentPage=1&size=10
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "records": [
            {
                "id": 1,
                "orderId": 1,
                "addressId": 1,
                "companyName": "顺丰快递",
                "trackingNumber": "SF1234567890",
                "expectedArrivalTime": "2024-01-02 14:00:00",
                "status": 1,
                "createdAt": "2024-01-01 12:00:00",
                "updatedAt": "2024-01-01 12:00:00",
                "order": {
                    "id": 1,
                    "farmerId": 1,
                    "productId": 1,
                    // 其他订单信息...
                }
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1,
        "pages": 1
    }
}

查询参数说明：
- orderId: 订单ID（选填）
- farmerId: 农户ID（选填）
- status: 物流状态（选填，0待发货,1已发货,2已签收,3已取消）
- currentPage: 当前页码（选填，默认1）
- size: 每页记录数（选填，默认10）


8. 批量删除物流信息
URL: DELETE /logistics/batch?ids=1,2,3
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": null
}

状态说明：
- 物流状态(status):
  0: 待发货
  1: 已发货
  2: 已签收
  3: 已取消

注意事项：
1. 创建物流信息时，订单必须处于已支付状态
2. 只有已发货的物流才能进行签收操作
3. 只有待发货或已取消的物流信息才能删除
4. 物流状态变更会同步更新订单状态
5. 订单退款成功后，相关物流信息会自动变更为已取消状态
6. 预计到达时间用于给用户提供预期送达时间参考 