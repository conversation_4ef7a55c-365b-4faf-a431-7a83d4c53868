# 沙棘健康资讯评论功能说明

## 功能概述

资讯评论功能允许用户在浏览资讯时发表评论、回复他人评论以及点赞评论。评论支持树形结构展示，方便用户查看原评论与回复的关系。

## 接口说明

### 1. 创建评论

- **URL**: `/comment`
- **Method**: POST
- **描述**: 创建新评论或回复
- **请求参数**:
  ```json
  {
    "userId": 1,          // 评论用户ID
    "articleId": 2,       // 文章ID
    "content": "评论内容", // 评论内容
    "parentId": null      // 父评论ID，为null表示顶级评论，有值表示回复其他评论
  }
  ```
- **响应示例**:
  ```json
  {
    "code": "0",
    "msg": "成功",
    "data": {
      "id": 1,
      "userId": 1,
      "articleId": 2,
      "content": "评论内容",
      "parentId": null,
      "likeCount": 0,
      "status": 1,
      "createdAt": "2023-06-01 10:00:00",
      "updatedAt": "2023-06-01 10:00:00"
    }
  }
  ```

### 2. 获取文章评论（树形结构）

- **URL**: `/comment/article/{articleId}`
- **Method**: GET
- **描述**: 获取指定文章的所有评论，以树形结构返回
- **路径参数**: `articleId` - 文章ID
- **响应示例**:
  ```json
  {
    "code": "0",
    "msg": "成功",
    "data": [
      {
        "id": 1,
        "userId": 1,
        "articleId": 2,
        "content": "评论内容",
        "parentId": null,
        "likeCount": 5,
        "status": 1,
        "createdAt": "2023-06-01 10:00:00",
        "updatedAt": "2023-06-01 10:00:00",
        "user": {
          "id": 1,
          "username": "user1",
          "name": "用户1",
          "avatar": "avatar.jpg"
        },
        "replies": [
          {
            "id": 2,
            "userId": 3,
            "articleId": 2,
            "content": "回复内容",
            "parentId": 1,
            "likeCount": 2,
            "status": 1,
            "createdAt": "2023-06-01 11:00:00",
            "updatedAt": "2023-06-01 11:00:00",
            "user": {
              "id": 3,
              "username": "user3",
              "name": "用户3",
              "avatar": "avatar3.jpg"
            },
            "replies": []
          }
        ]
      }
    ]
  }
  ```

### 3. 获取评论分页列表

- **URL**: `/comment/page`
- **Method**: GET
- **描述**: 分页获取评论列表
- **请求参数**:
  - `articleId`: 文章ID（可选）
  - `status`: 评论状态（可选）
  - `currentPage`: 当前页码，默认1
  - `size`: 每页大小，默认10
- **响应示例**:
  ```json
  {
    "code": "0",
    "msg": "成功",
    "data": {
      "records": [评论列表],
      "total": 100,
      "size": 10,
      "current": 1,
      "pages": 10
    }
  }
  ```

### 4. 删除评论

- **URL**: `/comment/{id}`
- **Method**: DELETE
- **描述**: 删除指定评论及其所有回复
- **路径参数**: `id` - 评论ID

### 5. 更新评论

- **URL**: `/comment/{id}`
- **Method**: PUT
- **描述**: 更新评论内容或状态
- **路径参数**: `id` - 评论ID
- **请求参数**:
  ```json
  {
    "content": "更新后的评论内容",
    "status": 1
  }
  ```

### 6. 点赞评论

- **URL**: `/comment/{id}/like`
- **Method**: POST
- **描述**: 给评论点赞
- **路径参数**: `id` - 评论ID

## 文章详情中查看评论

在查看文章详情时，系统会自动返回该文章的评论列表。调用文章详情接口 `/article/{id}` 将会返回包含文章信息和评论信息的完整数据。

## 数据库表结构

```sql
CREATE TABLE `comment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `article_id` bigint(20) NOT NULL COMMENT '文章ID',
  `content` text NOT NULL COMMENT '评论内容',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父评论ID，用于回复功能，为空表示顶级评论',
  `like_count` int(11) DEFAULT '0' COMMENT '点赞数',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态：0-隐藏，1-显示',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_article_id` (`article_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';
``` 