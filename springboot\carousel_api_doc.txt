农产品销售系统 - 轮播图模块接口文档

1. 创建轮播图
URL: /carousel
Method: POST
描述: 创建一个新的轮播图项目
请求体:
{
    "imageUrl": "string",     // 轮播图片URL，必填
    "tag": "string",          // 标签文本，选填
    "title": "string",        // 标题，必填
    "description": "string",  // 描述文本，选填
    "productId": "number",    // 关联商品ID，必填，0表示不关联商品
    "sortOrder": "number",    // 排序顺序，选填，默认0
    "status": "number"        // 状态：0-禁用，1-启用，选填，默认1
}
响应:
{
    "code": "string",         // 状态码，0表示成功
    "msg": "string",          // 响应消息
    "data": {                 // 创建的轮播图数据
        "id": "number",
        ...轮播图完整信息
    }
}

2. 更新轮播图
URL: /carousel/{id}
Method: PUT
描述: 更新指定ID的轮播图信息
路径参数:
- id: 轮播图ID
请求体: 同创建轮播图
响应: 同创建轮播图

3. 删除轮播图
URL: /carousel/{id}
Method: DELETE
描述: 删除指定ID的轮播图
路径参数:
- id: 轮播图ID
响应:
{
    "code": "string",         // 状态码，0表示成功
    "msg": "string",          // 响应消息
    "data": null
}

4. 获取轮播图详情
URL: /carousel/{id}
Method: GET
描述: 获取指定ID的轮播图详细信息
路径参数:
- id: 轮播图ID
响应:
{
    "code": "string",         // 状态码，0表示成功
    "msg": "string",          // 响应消息
    "data": {                 // 轮播图详细信息
        "id": "number",
        "imageUrl": "string",
        "tag": "string",
        "title": "string",
        "description": "string",
        "productId": "number",
        "sortOrder": "number",
        "status": "number",
        "product": {          // 关联的商品信息（如果有）
            "id": "number",
            "name": "string",
            ...商品其他信息
        }
    }
}

5. 获取所有启用的轮播图
URL: /carousel/active
Method: GET
描述: 获取所有状态为启用的轮播图列表，按排序顺序升序排列
响应:
{
    "code": "string",         // 状态码，0表示成功
    "msg": "string",          // 响应消息
    "data": [                 // 轮播图列表
        {
            "id": "number",
            ...轮播图完整信息
        }
    ]
}

6. 分页获取轮播图列表
URL: /carousel/page
Method: GET
描述: 分页获取轮播图列表
请求参数:
- currentPage: 当前页码，默认1
- size: 每页数量，默认10
响应:
{
    "code": "string",         // 状态码，0表示成功
    "msg": "string",          // 响应消息
    "data": {
        "records": [          // 轮播图列表
            {
                "id": "number",
                ...轮播图完整信息
            }
        ],
        "total": "number",    // 总记录数
        "size": "number",     // 每页大小
        "current": "number",  // 当前页码
        "pages": "number"     // 总页数
    }
}

注意事项：
1. 所有接口返回的数据都使用统一的Result对象封装
2. 创建和更新轮播图时会检查关联的商品是否存在
3. 轮播图排序按sortOrder字段升序排列
4. 获取active轮播图时只返回状态为1（启用）的记录
5. 关联的商品信息会自动填充到返回结果中
6. 所有接口都需要登录权限，管理相关接口需要管理员权限 