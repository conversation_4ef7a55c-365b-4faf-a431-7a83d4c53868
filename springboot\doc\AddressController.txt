地址管理接口文档

1. 创建地址
URL: POST /address
请求示例:
{
    "userId": 1,
    "province": "浙江省",
    "city": "杭州市",
    "district": "西湖区",
    "detail": "文三路123号",
    "receiver": "张三",
    "phone": "13800138000"
}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "userId": 1,
        "province": "浙江省",
        "city": "杭州市",
        "district": "西湖区",
        "detail": "文三路123号",
        "receiver": "张三",
        "phone": "13800138000"
    }
}

2. 更新地址信息
URL: PUT /address/{id}
请求示例:
{
    "province": "浙江省",
    "city": "宁波市",
    "district": "鄞州区",
    "detail": "中河街道123号"
}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "userId": 1,
        "province": "浙江省",
        "city": "宁波市",
        "district": "鄞州区",
        "detail": "中河街道123号",
        "receiver": "张三",
        "phone": "13800138000"
    }
}

3. 删除地址
URL: DELETE /address/{id}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": null
}

4. 根据ID获取地址详情
URL: GET /address/{id}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "userId": 1,
        "province": "浙江省",
        "city": "杭州市",
        "district": "西湖区",
        "detail": "文三路123号",
        "receiver": "张三",
        "phone": "13800138000"
    }
}

5. 根据用户ID获取地址列表
URL: GET /address/user/{userId}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": [
        {
            "id": 1,
            "userId": 1,
            "province": "浙江省",
            "city": "杭州市",
            "district": "西湖区",
            "detail": "文三路123号",
            "receiver": "张三",
            "phone": "13800138000"
        }
    ]
}

6. 分页查询地址列表
URL: GET /address/page?userId=1&currentPage=1&size=10
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "records": [
            {
                "id": 1,
                "userId": 1,
                "province": "浙江省",
                "city": "杭州市",
                "district": "西湖区",
                "detail": "文三路123号",
                "receiver": "张三",
                "phone": "13800138000"
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1,
        "pages": 1
    }
}

7. 批量删除地址
URL: DELETE /address/batch?ids=1,2,3
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": null
} 