用户管理接口文档

1. 根据id获取用户信息
URL: GET /user/{id}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "username": "test",
        "email": "<EMAIL>",
        "role": "user",
        "status": 1
    }
}

2. 根据username获取用户信息
URL: GET /user/username/{username}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "username": "test",
        "email": "<EMAIL>",
        "role": "user",
        "status": 1
    }
}

3. 根据email获取用户信息
URL: GET /user/email/{email}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "username": "test",
        "email": "<EMAIL>",
        "role": "user",
        "status": 1
    }
}

4. 分页查询用户
URL: GET /user/page?username=test&currentPage=1&size=10
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "records": [
            {
                "id": 1,
                "username": "test",
                "email": "<EMAIL>",
                "role": "user",
                "status": 1
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1,
        "pages": 1
    }
}

5. 创建新用户
URL: POST /user
请求示例:
{
    "username": "test",
    "password": "123456",
    "email": "<EMAIL>"
}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "username": "test",
        "email": "<EMAIL>",
        "role": "user",
        "status": 1
    }
}

6. 更新用户信息
URL: PUT /user/{id}
请求示例:
{
    "username": "test",
    "email": "<EMAIL>"
}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "username": "test",
        "email": "<EMAIL>",
        "role": "user",
        "status": 1
    }
}

7. 根据id删除用户
URL: DELETE /user/delete/{id}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": null
} 