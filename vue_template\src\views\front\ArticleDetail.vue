<template>
  <div class="article-detail-page">
    <front-header></front-header>
    <div class="main-content" v-loading="loading">
      <!-- 文章内容区 -->
      <div class="article-container" v-if="article">
        <h1 class="article-title">{{ article.title }}</h1>
        
        <!-- 文章摘要 -->
        <div class="article-summary" v-if="article.summary">
          <i class="el-icon-info-filled"></i>
          {{ article.summary }}
        </div>
        
        <div class="article-meta">
          <div class="meta-left">
            <span class="publish-time">
              <i class="el-icon-time"></i>
              {{ formatTime(article.createdAt) }}
            </span>
            <span class="view-count">
              <i class="el-icon-view"></i>
              {{ article.viewCount }} 阅读
            </span>
          </div>
          <div class="meta-right">
            <el-button type="text" icon="el-icon-back" @click="$router.push('/articles')">
              返回列表
            </el-button>
          </div>
        </div>

        <!-- 封面图 -->
        <div class="cover-image" v-if="article.coverImage">
          <el-image 
            :src="article.coverImage?.startsWith('http') ? article.coverImage : `/api${article.coverImage}`"
            fit="cover">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </div>

        <!-- 文章内容 -->
        <div class="article-content" v-html="article.content"></div>
      </div>

      <!-- 评论区 -->
      <div class="comments-section" v-if="article">
        <h3 class="comments-title">
          <i class="el-icon-chat-line-round"></i> 评论区
        </h3>
        
        <!-- 评论表单 -->
        <div class="comment-form">
          <el-input
            type="textarea"
            :rows="3"
            placeholder="写下您的评论..."
            v-model="commentForm.content"
            maxlength="500"
            show-word-limit
          ></el-input>
          <div class="comment-form-actions">
            <el-button type="primary" @click="submitComment" :disabled="!isLoggedIn || !commentForm.content.trim()">
              发表评论
            </el-button>
            <p class="login-tip" v-if="!isLoggedIn">请先 <a @click="$router.push('/login')">登录</a> 后评论</p>
          </div>
        </div>
        
        <!-- 评论列表 -->
        <div class="comments-list" v-if="comments && comments.length > 0">
          <div v-for="comment in comments" :key="comment.id" class="comment-item">
            <div class="comment-avatar">
              <el-avatar :size="40" :src="comment.user?.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'">
                {{ comment.user?.name?.charAt(0) || 'U' }}
              </el-avatar>
            </div>
            <div class="comment-content">
              <div class="comment-header">
                <span class="comment-author">{{ comment.user?.name || '匿名用户' }}</span>
                <span class="comment-time">{{ formatTime(comment.createdAt) }}</span>
              </div>
              <div class="comment-text">{{ comment.content }}</div>
              <div class="comment-actions">
                <el-button type="text" size="mini" @click="likeComment(comment)">
                  <i class="el-icon-star-off"></i> 
                  点赞 {{ comment.likeCount || 0 }}
                </el-button>
                <el-button type="text" size="mini" @click="replyToComment(comment)" v-if="isLoggedIn">
                  <i class="el-icon-chat-line-round"></i> 
                  回复
                </el-button>
              </div>
              
              <!-- 回复表单 -->
              <div class="reply-form" v-if="replyTo === comment.id">
                <el-input
                  type="textarea"
                  :rows="2"
                  placeholder="回复评论..."
                  v-model="replyForm.content"
                  maxlength="200"
                  show-word-limit
                ></el-input>
                <div class="reply-form-actions">
                  <el-button size="mini" @click="cancelReply">取消</el-button>
                  <el-button type="primary" size="mini" @click="submitReply(comment.id)" :disabled="!replyForm.content.trim()">
                    提交回复
                  </el-button>
                </div>
              </div>
              
              <!-- 回复列表 -->
              <div class="replies-list" v-if="comment.replies && comment.replies.length > 0">
                <div v-for="reply in comment.replies" :key="reply.id" class="reply-item">
                  <div class="reply-avatar">
                    <el-avatar :size="30" :src="reply.user?.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'">
                      {{ reply.user?.name?.charAt(0) || 'U' }}
                    </el-avatar>
                  </div>
                  <div class="reply-content">
                    <div class="reply-header">
                      <span class="reply-author">{{ reply.user?.name || '匿名用户' }}</span>
                      <span class="reply-time">{{ formatTime(reply.createdAt) }}</span>
                    </div>
                    <div class="reply-text">{{ reply.content }}</div>
                    <div class="reply-actions">
                      <el-button type="text" size="mini" @click="likeComment(reply)">
                        <i class="el-icon-star-off"></i> 
                        点赞 {{ reply.likeCount || 0 }}
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 没有评论时的提示 -->
        <div class="no-comments" v-else>
          <el-empty description="暂无评论，快来抢沙发吧！" :image-size="150"></el-empty>
        </div>
      </div>

      <!-- 空状态 -->
      <el-empty v-else-if="!loading" description="文章不存在或已被删除">
        <el-button type="primary" @click="$router.push('/articles')">返回列表</el-button>
      </el-empty>
    </div>
    <front-footer></front-footer>
  </div>
</template>

<script>
import FrontHeader from '@/components/front/FrontHeader.vue'
import FrontFooter from '@/components/front/FrontFooter.vue'
import Request from '@/utils/request'
import { formatTime } from '@/utils/time'

export default {
  name: 'ArticleDetail',
  components: {
    FrontHeader,
    FrontFooter
  },
  data() {
    return {
      loading: false,
      article: null,
      comments: [],
      isLoggedIn: false,
      currentUser: null,
      commentForm: {
        content: '',
        articleId: null,
        userId: null
      },
      replyTo: null,
      replyForm: {
        content: '',
        articleId: null,
        userId: null,
        parentId: null
      }
    }
  },
  created() {
    this.getArticleDetail()
    this.checkLoginStatus()
  },
  methods: {
    formatTime,
    checkLoginStatus() {
      const userJson = localStorage.getItem('user')
      if (userJson) {
        try {
          const userData = JSON.parse(userJson)
          this.isLoggedIn = true
          this.currentUser = userData
          this.commentForm.userId = userData.id
          this.replyForm.userId = userData.id
        } catch (error) {
          console.error('解析用户数据失败:', error)
        }
      }
    },
    async getArticleDetail() {
      const id = this.$route.params.id
      if (!id) {
        this.$router.push('/articles')
        return
      }

      this.loading = true
      try {
        const res = await Request.get(`/article/${id}`)
        if (res.code === '0') {
          // 新的响应格式中，文章内容在 data.article 中
          if (res.data && res.data.article) {
            this.article = res.data.article
            this.comments = res.data.comments || []
            this.commentForm.articleId = id
            this.replyForm.articleId = id
          } else {
            // 兼容旧格式
            this.article = res.data
          }
        } else {
          this.$message.error(res.msg||'获取文章详情失败')
        }
      } catch (error) {
        console.error('获取文章详情失败:', error)
        this.$message.error(error.message||'获取文章详情失败')
      } finally {
        this.loading = false
      }
    },
    async submitComment() {
      if (!this.isLoggedIn) {
        this.$message.warning('请先登录后再评论')
        return
      }
      
      if (!this.commentForm.content.trim()) {
        this.$message.warning('评论内容不能为空')
        return
      }
      
      try {
        const response = await Request.post('/comment', {
          userId: this.currentUser.id,
          articleId: this.article.id,
          content: this.commentForm.content.trim(),
          parentId: null
        })
        
        if (response.code === '0') {
          this.$message.success('评论发布成功')
          this.commentForm.content = ''
          // 重新加载评论
          this.getArticleDetail()
        } else {
          this.$message.error(response.msg || '评论发布失败')
        }
      } catch (error) {
        console.error('发布评论失败:', error)
        this.$message.error('评论发布失败，请稍后再试')
      }
    },
    replyToComment(comment) {
      this.replyTo = comment.id
      this.replyForm.parentId = comment.id
    },
    cancelReply() {
      this.replyTo = null
      this.replyForm.content = ''
      this.replyForm.parentId = null
    },
    async submitReply(parentId) {
      if (!this.isLoggedIn) {
        this.$message.warning('请先登录后再回复')
        return
      }
      
      if (!this.replyForm.content.trim()) {
        this.$message.warning('回复内容不能为空')
        return
      }
      
      try {
        const response = await Request.post('/comment', {
          userId: this.currentUser.id,
          articleId: this.article.id,
          content: this.replyForm.content.trim(),
          parentId: parentId
        })
        
        if (response.code === '0') {
          this.$message.success('回复发布成功')
          this.replyForm.content = ''
          this.replyTo = null
          // 重新加载评论
          this.getArticleDetail()
        } else {
          this.$message.error(response.msg || '回复发布失败')
        }
      } catch (error) {
        console.error('发布回复失败:', error)
        this.$message.error('回复发布失败，请稍后再试')
      }
    },
    async likeComment(comment) {
      try {
        const response = await Request.post(`/comment/${comment.id}/like`)
        if (response.code === '0') {
          comment.likeCount = (comment.likeCount || 0) + 1
          this.$message.success('点赞成功')
        } else {
          this.$message.error(response.msg || '点赞失败')
        }
      } catch (error) {
        console.error('点赞失败:', error)
        this.$message.error('点赞失败，请稍后再试')
      }
    }
  }
}
</script>

<style scoped>
.article-detail-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f8faf5 0%, #f5f7fa 100%);
}

.main-content {
  flex: 1;
  padding: 32px;
  max-width: 1000px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.article-container {
  background: white;
  padding: 32px;
  border-radius: 12px;
  border: 1px solid #ebeef5;
  position: relative;
  overflow: hidden;
  margin-bottom: 32px;
}

.article-container::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, #f56c0c, #e6a23c);
}

.article-title {
  font-size: 28px;
  margin-top: 0;
  margin-bottom: 20px;
  color: #303133;
  font-weight: 600;
  line-height: 1.3;
}

.article-summary {
  background-color: #f8f9fa;
  border-left: 4px solid #e6a23c;
  padding: 12px 16px;
  margin-bottom: 20px;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  font-size: 14px;
  color: #909399;
}

.meta-left {
  display: flex;
  gap: 16px;
}

.cover-image {
  margin-bottom: 24px;
  border-radius: 8px;
  overflow: hidden;
  max-height: 400px;
}

.cover-image .el-image {
  width: 100%;
  max-height: 400px;
}

.article-content {
  font-size: 16px;
  line-height: 1.8;
  color: #303133;
}

.article-content >>> img {
  max-width: 100%;
  height: auto;
  margin: 10px 0;
  border-radius: 8px;
}

.article-content >>> h1, 
.article-content >>> h2,
.article-content >>> h3 {
  margin-top: 28px;
  margin-bottom: 16px;
  color: #303133;
}

.article-content >>> p {
  margin-bottom: 16px;
}

/* 评论区样式 */
.comments-section {
  background: white;
  padding: 32px;
  border-radius: 12px;
  border: 1px solid #ebeef5;
}

.comments-title {
  font-size: 20px;
  margin-top: 0;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #303133;
}

.comments-title i {
  color: #f56c0c;
}

.comment-form {
  margin-bottom: 32px;
}

.comment-form-actions {
  margin-top: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.login-tip {
  font-size: 14px;
  color: #909399;
}

.login-tip a {
  color: #f56c0c;
  cursor: pointer;
}

.comments-list {
  border-top: 1px solid #ebeef5;
  padding-top: 24px;
}

.comment-item {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.comment-author {
  font-weight: 500;
  font-size: 15px;
  color: #303133;
}

.comment-time {
  font-size: 13px;
  color: #909399;
}

.comment-text {
  font-size: 14px;
  line-height: 1.6;
  color: #606266;
  margin-bottom: 8px;
}

.comment-actions {
  display: flex;
  gap: 16px;
}

.reply-form {
  margin-top: 12px;
  margin-bottom: 16px;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
}

.reply-form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
  gap: 8px;
}

.replies-list {
  background: #f8f9fa;
  padding: 16px;
  margin-top: 12px;
  border-radius: 8px;
}

.reply-item {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.reply-item:last-child {
  margin-bottom: 0;
}

.reply-content {
  flex: 1;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.reply-author {
  font-weight: 500;
  font-size: 14px;
  color: #303133;
}

.reply-time {
  font-size: 12px;
  color: #909399;
}

.reply-text {
  font-size: 13px;
  line-height: 1.5;
  color: #606266;
  margin-bottom: 4px;
}

.reply-actions {
  display: flex;
  gap: 12px;
}

.no-comments {
  padding: 32px 0;
  text-align: center;
}
</style> 