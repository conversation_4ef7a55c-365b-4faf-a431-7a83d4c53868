通知管理接口文档

1. 获取所有通知
URL: GET /notice
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": [
        {
            "id": 1,
            "title": "系统维护通知",
            "content": "系统将于今晚12点进行维护",
            "time": "2023-11-10 12:00:00"
        }
    ]
}

2. 获取最新通知
URL: GET /notice/limit?count=5
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": [
        {
            "id": 1,
            "title": "系统维护通知",
            "content": "系统将于今晚12点进行维护",
            "time": "2023-11-10 12:00:00"
        }
    ]
}

3. 分页查询通知
URL: GET /notice/page?title=系统维护&currentPage=1&size=10
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "records": [
            {
                "id": 1,
                "title": "系统维护通知",
                "content": "系统将于今晚12点进行维护",
                "time": "2023-11-10 12:00:00"
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1,
        "pages": 1
    }
}

4. 根据id获取通知
URL: GET /notice/{id}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "title": "系统维护通知",
        "content": "系统将于今晚12点进行维护",
        "time": "2023-11-10 12:00:00"
    }
}

5. 新增通知
URL: POST /notice
请求示例:
{
    "title": "系统维护通知",
    "content": "系统将于今晚12点进行维护"
}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "title": "系统维护通知",
        "content": "系统将于今晚12点进行维护",
        "time": "2023-11-10 12:00:00"
    }
}

6. 更新通知
URL: PUT /notice/{id}
请求示例:
{
    "title": "系统维护通知",
    "content": "系统将于今晚12点进行维护"
}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": {
        "id": 1,
        "title": "系统维护通知",
        "content": "系统将于今晚12点进行维护",
        "time": "2023-11-10 12:00:00"
    }
}

7. 批量删除通知
URL: DELETE /notice/deleteBatch?ids=1,2,3
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": null
}

8. 根据id删除通知
URL: DELETE /notice/{id}
返回成功示例:
{
    "code": "0",
    "msg": "成功",
    "data": null
} 