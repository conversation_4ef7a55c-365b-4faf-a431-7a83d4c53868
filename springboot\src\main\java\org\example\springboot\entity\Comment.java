package org.example.springboot.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.sql.Timestamp;

@Data
@Schema(description = "评论实体")
public class Comment {
    @TableId(type = IdType.AUTO)
    @Schema(description = "评论ID")
    private Long id;

    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID")
    private Long userId;

    @NotNull(message = "文章ID不能为空")
    @Schema(description = "文章ID")
    private Long articleId;

    @NotBlank(message = "评论内容不能为空")
    @Schema(description = "评论内容")
    private String content;

    @Schema(description = "父评论ID，用于回复功能，为空表示顶级评论")
    private Long parentId;

    @Schema(description = "点赞数")
    private Integer likeCount = 0;

    @Schema(description = "状态：0-隐藏，1-显示")
    private Integer status = 1;

    @Schema(description = "创建时间")
    private Timestamp createdAt;

    @Schema(description = "更新时间")
    private Timestamp updatedAt;
    
    @TableField(exist = false)
    @Schema(description = "用户信息")
    private User user;
    
    @TableField(exist = false)
    @Schema(description = "回复列表")
    private java.util.List<Comment> replies;
} 